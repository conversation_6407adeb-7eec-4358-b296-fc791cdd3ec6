{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/cherry-editor.tsx"], "sourcesContent": ["'use client'\n\nimport dynamic from 'next/dynamic'\nimport { useState, useEffect } from 'react'\n\n// Dynamically import MDEditor to avoid SSR issues\nconst MDEditor = dynamic(\n  () => import('@uiw/react-md-editor'),\n  {\n    ssr: false,\n    loading: () => (\n      <div className=\"w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse min-h-[400px] flex items-center justify-center\">\n        <div className=\"text-muted-foreground\">Loading editor...</div>\n      </div>\n    )\n  }\n)\n\ninterface CherryEditorProps {\n  value?: string\n  onChange?: (value: string) => void\n  minHeight?: string\n  placeholder?: string\n}\n\nexport function CherryEditor({\n  value = '',\n  onChange,\n  minHeight = '500px',\n  placeholder = 'Start writing your amazing content...'\n}: CherryEditorProps) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Show loading state during SSR\n  if (!mounted) {\n    return (\n      <div\n        className=\"w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse flex items-center justify-center\"\n        style={{ minHeight }}\n      >\n        <div className=\"text-muted-foreground\">Loading editor...</div>\n      </div>\n    )\n  }\n\n\n\n  try {\n    return (\n      <div\n        className=\"w-full md-editor-wrapper\"\n        style={{ minHeight }}\n        data-color-mode=\"auto\"\n      >\n        <MDEditor\n          value={value}\n          onChange={(val) => onChange?.(val || '')}\n          preview=\"live\"\n          hideToolbar={false}\n          visibleDragbar={false}\n          textareaProps={{\n            placeholder,\n            style: {\n              fontSize: 14,\n              lineHeight: 1.6,\n              fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace',\n              // backgroundColor: 'transparent', // Removed to ensure background color is applied from parent style\n              color: 'inherit'\n            }\n          }}\n          style={{\n            backgroundColor: 'hsl(var(--background))',\n            color: 'hsl(var(--foreground))',\n            minHeight: minHeight\n          }}\n        />\n      </div>\n    )\n  } catch (error) {\n    console.error('Error rendering MDEditor:', error)\n    return (\n      <div className=\"w-full p-4 border border-input bg-background text-foreground rounded-md\">\n        <div className=\"text-red-500 mb-2\">Error loading editor</div>\n        <textarea\n          value={value}\n          onChange={(e) => onChange?.(e.target.value)}\n          placeholder={placeholder}\n          className=\"w-full h-full bg-transparent border-0 outline-0 resize-none text-foreground\"\n          style={{ minHeight: `calc(${minHeight} - 2rem)` }}\n        />\n      </div>\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;AAHA;;;;AAKA,kDAAkD;AAClD,MAAM,WAAW,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAGnB,KAAK;IACL,SAAS,kBACP,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAwB;;;;;;;;;;;;AAaxC,SAAS,aAAa,EAC3B,QAAQ,EAAE,EACV,QAAQ,EACR,YAAY,OAAO,EACnB,cAAc,uCAAuC,EACnC;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,gCAAgC;IAChC,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE;YAAU;sBAEnB,cAAA,8OAAC;gBAAI,WAAU;0BAAwB;;;;;;;;;;;IAG7C;IAIA,IAAI;QACF,qBACE,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE;YAAU;YACnB,mBAAgB;sBAEhB,cAAA,8OAAC;gBACC,OAAO;gBACP,UAAU,CAAC,MAAQ,WAAW,OAAO;gBACrC,SAAQ;gBACR,aAAa;gBACb,gBAAgB;gBAChB,eAAe;oBACb;oBACA,OAAO;wBACL,UAAU;wBACV,YAAY;wBACZ,YAAY;wBACZ,qGAAqG;wBACrG,OAAO;oBACT;gBACF;gBACA,OAAO;oBACL,iBAAiB;oBACjB,OAAO;oBACP,WAAW;gBACb;;;;;;;;;;;IAIR,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAAoB;;;;;;8BACnC,8OAAC;oBACC,OAAO;oBACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oBAC1C,aAAa;oBACb,WAAU;oBACV,OAAO;wBAAE,WAAW,CAAC,KAAK,EAAE,UAAU,QAAQ,CAAC;oBAAC;;;;;;;;;;;;IAIxD;AACF", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/new-post/new-post-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { CherryEditor } from '@/components/cherry-editor'\nimport { createClient } from '@/lib/supabase/client'\n\nexport default function NewPostForm() {\n  const [title, setTitle] = useState('')\n  const [content, setContent] = useState('')\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [error, setError] = useState('')\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!title.trim() || !content.trim()) {\n      setError('Title and content are required')\n      return\n    }\n\n    setIsSubmitting(true)\n    setError('')\n\n    try {\n      const supabase = createClient()\n      \n      const { error: insertError } = await supabase\n        .from('posts')\n        .insert([\n          {\n            title: title.trim(),\n            content: content.trim()\n          }\n        ])\n\n      if (insertError) {\n        throw insertError\n      }\n\n      // Redirect to homepage on success\n      router.push('/')\n      router.refresh()\n    } catch (err) {\n      console.error('Error creating post:', err)\n      setError('Error creating post. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <div className=\"animate-fade-in\">\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {error && (\n          <div className=\"bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-lg animate-slide-in\">\n            <div className=\"flex items-center\">\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              {error}\n            </div>\n          </div>\n        )}\n\n        <div className=\"bg-card rounded-xl border border-border p-8 shadow-sm hover-lift\">\n          <div className=\"space-y-6\">\n            <div>\n              <label htmlFor=\"title\" className=\"flex items-center text-sm font-medium text-foreground mb-3\">\n                <svg className=\"w-4 h-4 mr-2 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z\" />\n                </svg>\n                Post Title\n              </label>\n              <input\n                type=\"text\"\n                id=\"title\"\n                value={title}\n                onChange={(e) => setTitle(e.target.value)}\n                className=\"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 text-lg\"\n                placeholder=\"Enter an engaging title...\"\n                disabled={isSubmitting}\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"content\" className=\"flex items-center text-sm font-medium text-foreground mb-3\">\n                <svg className=\"w-4 h-4 mr-2 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                </svg>\n                Post Content\n              </label>\n              <div className=\"border border-input rounded-lg overflow-hidden shadow-sm\">\n                <CherryEditor\n                  value={content}\n                  onChange={setContent}\n                  // height=\"600px\" // Removed fixed height to allow content to expand\n                  placeholder=\"Start writing your amazing content...\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex items-center gap-4 pt-4\">\n          <button\n            type=\"submit\"\n            disabled={isSubmitting || !title.trim() || !content.trim()}\n            className=\"btn-primary bg-primary text-primary-foreground px-8 py-3 rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium flex items-center\"\n          >\n            {isSubmitting ? (\n              <>\n                <div className=\"w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent\" />\n                Publishing...\n              </>\n            ) : (\n              <>\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n                </svg>\n                Publish Post\n              </>\n            )}\n          </button>\n\n          <Link\n            href=\"/\"\n            className=\"bg-secondary text-secondary-foreground px-8 py-3 rounded-lg hover:bg-secondary/80 transition-all duration-200 font-medium flex items-center\"\n          >\n            <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n            Cancel\n          </Link>\n        </div>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;YACpC,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;YAE5B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC;gBACN;oBACE,OAAO,MAAM,IAAI;oBACjB,SAAS,QAAQ,IAAI;gBACvB;aACD;YAEH,IAAI,aAAa;gBACf,MAAM;YACR;YAEA,kCAAkC;YAClC,OAAO,IAAI,CAAC;YACZ,OAAO,OAAO;QAChB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;;gBACrC,uBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACtE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BAEtE;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAQ,WAAU;;0DAC/B,8OAAC;gDAAI,WAAU;gDAA4B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACnF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;wCACZ,UAAU;;;;;;;;;;;;0CAId,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAU,WAAU;;0DACjC,8OAAC;gDAAI,WAAU;gDAA4B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACnF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sIAAA,CAAA,eAAY;4CACX,OAAO;4CACP,UAAU;4CACV,oEAAoE;4CACpE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,UAAU,gBAAgB,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI;4BACxD,WAAU;sCAET,6BACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCAAwF;;6DAIzG;;kDACE,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;;sCAMZ,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}]}