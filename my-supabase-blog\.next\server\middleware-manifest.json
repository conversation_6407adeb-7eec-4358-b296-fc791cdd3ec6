{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "i23zSbxJiDMoUk5/ztQUmXHArHW0SSwiU1CFv1OsRmU=", "__NEXT_PREVIEW_MODE_ID": "0382b648ec51b420b5a286540193df5a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b42e3e371a37a0b5bfa2dccfb9a9c908222663c941705a01d2eb39982e19ca02", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b2596642b05ac1c62ca7d54b2ebdf36a1f1e47b4dffa4d211eb15d451a421261"}}}, "sortedMiddleware": ["/"], "functions": {}}